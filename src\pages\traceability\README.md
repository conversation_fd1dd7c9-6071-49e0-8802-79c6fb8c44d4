# 溯源管理功能

## 功能概述

参考 EnvAirAlarmList 实现的溯源管理功能，包含列表展示、查看详情和处理功能。

## 主要特性

### 1. 列表功能
- **数据展示**: 报警ID、高风险企业、低风险企业、污染类型、溯源指标、相关报警值、溯源时间
- **筛选功能**: 支持按报警ID、高风险企业名称、低风险企业名称、污染类型、溯源指标、溯源时间进行筛选
- **分页**: 支持分页展示数据
- **序号**: 自动生成序号

### 2. 操作功能
- **查看**: 查看溯源记录详情
- **处理**: 对溯源记录进行处理操作

### 3. 弹窗功能
- **查看模式**: 显示溯源记录的详细信息（只读）
- **处理模式**: 允许填写处理人、处理内容、处理结果等信息

## 技术实现

### API 接口 (traceApi.ts)
- `queryTraceRecordList`: 查询溯源记录列表
- `handleTraceRecord`: 处理溯源记录
- `queryTraceRecordDetail`: 查询溯源记录详情
- `generateTraceReport`: 生成溯源报告

### 组件结构
```
src/pages/traceability/
├── index.tsx                    # 主页面组件
├── TraceabilityModal.tsx        # 弹窗组件
├── traceability.module.less     # 样式文件
└── README.md                    # 说明文档
```

### 数据类型
```typescript
interface TraceRecord {
  id: string;                        // 主键
  alarmId: string;                   // 报警id
  highRiskEnterpriseId: string;      // 高风险企业id
  highRiskEnterpriseName: string;    // 高风险企业名称
  lowRiskEnterpriseId: string;       // 低风险企业id
  lowRiskEnterpriseName: string;     // 低风险企业名称
  pollutantConcentration: number;    // 相关报警值
  pollutionType: string;             // 污染类型
  tracingCode: string;               // 溯源指标
  tracingTime: string;               // 溯源时间
  isDelete: number;                  // 是否删除
}
```

## 使用方式

1. **导入页面组件**:
```tsx
import TraceabilityList from '@/pages/traceability';
```

2. **路由配置**:
```tsx
{
  path: '/traceability',
  component: TraceabilityList,
}
```

## 样式说明

- 复用了 `envAlarm/common.module.less` 中的通用样式
- 自定义样式在 `traceability.module.less` 中定义
- 状态显示使用不同颜色：待处理(红色)、已处理(绿色)

## 注意事项

1. **数据结构**: 基于后端实际返回的数据结构进行开发
2. **数据验证**: 处理时需要填写必要的处理信息
3. **列表刷新**: 处理完成后会自动刷新列表
4. **错误处理**: 包含完整的错误提示和异常处理
5. **数据展示**: 相关报警值会保留两位小数显示

## 扩展功能

可以根据需要添加以下功能：
- 批量处理
- 导出功能
- 统计报表
- 溯源报告生成
- 历史记录查看
