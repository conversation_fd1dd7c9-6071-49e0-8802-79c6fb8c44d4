import { envRequest } from '@/request';

// ===== 类型定义 =====

/**
 * 通用 API 响应接口
 */
export type ApiResponse<T = unknown> = {
  code: number;
  data: T;
  msg?: string;
  total?: number;
};

/**
 * 分页查询参数接口
 */
interface PageQueryParams {
  aescs?: string[];
  descs?: string[];
  condition: Record<string, unknown>;
  currentPage: number;
  pageSize: number;
}

/**
 * 溯源记录数据接口
 */
export interface TraceRecord {
  id: string; // 主键
  alarmId: string; // 报警id
  highRiskEnterpriseId: string; // 高风险企业id
  highRiskEnterpriseName: string; // 高风险企业名称
  lowRiskEnterpriseId: string; // 低风险企业id
  lowRiskEnterpriseName: string; // 低风险企业名称
  pollutantConcentration: number; // 相关报警值
  pollutionType: string; // 污染类型
  tracingCode: string; // 溯源指标
  tracingTime: string; // 溯源时间
  isDelete: number; // 是否删除
  serialNo?: number; // 序号（前端添加）
}

/**
 * 溯源处理参数接口
 */
export interface TraceHandleParams {
  id: string;
  handlePsn: string; // 处理人
  handleTime: string; // 处理时间
  handleContent: string; // 处理内容
  handleResult: string; // 处理结果
}

/**
 * 溯源 API 接口定义
 */
interface TraceApiInterface {
  /** 查询溯源记录列表 */
  queryTraceRecordList: (data: PageQueryParams) => Promise<ApiResponse<TraceRecord[]>>;
  /** 处理溯源记录 */
  handleTraceRecord: (data: TraceHandleParams) => Promise<ApiResponse<unknown>>;
  /** 查询溯源记录详情 */
  queryTraceRecordDetail: (id: string) => Promise<ApiResponse<TraceRecord>>;
  /** 生成溯源报告 */
  generateTraceReport: (data: Record<string, unknown>) => Promise<ApiResponse<unknown>>;
}

/**
 * 溯源模块 API
 */
const traceApi: TraceApiInterface = {
  /** 查询溯源记录列表 */
  queryTraceRecordList: (data: PageQueryParams) => {
    return envRequest.post('/trace/page', { data });
  },

  /** 处理溯源记录 */
  handleTraceRecord: (data: TraceHandleParams) => {
    return envRequest.post('/trace/handle', { data });
  },

  /** 查询溯源记录详情 */
  queryTraceRecordDetail: (id: string) => {
    return envRequest.get(`/trace/detail/${id}`);
  },

  /** 生成溯源报告 */
  generateTraceReport: (data: Record<string, unknown>) => {
    return envRequest.post('/trace/generateReport', { data });
  },
};

export default traceApi;
